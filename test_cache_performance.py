#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工参缓存性能测试脚本
"""

import os
import sys
import time

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from routing.workparam_router import WorkParamRouter
from config.config_manager import config_manager
from utils.logger import setup_logger, get_logger

# 初始化日志
setup_logger()
logger = get_logger(__name__)


def test_cache_performance():
    """测试缓存性能"""
    print("🧪 工参缓存性能测试")
    print("="*60)
    
    # 创建路由器
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    
    print("📊 测试场景：连续10次配置路由器")
    print("-"*60)
    
    times = []
    
    for i in range(10):
        print(f"第 {i+1} 次配置...")
        
        start_time = time.time()
        success = router.configure(routing_config)
        end_time = time.time()
        
        elapsed = end_time - start_time
        times.append(elapsed)
        
        if success:
            stats = router.get_stats()
            print(f"  ✅ 配置成功，耗时: {elapsed:.3f}秒，工参记录: {stats['workparam_records']}条")
        else:
            print(f"  ❌ 配置失败，耗时: {elapsed:.3f}秒")
    
    print("\n" + "="*60)
    print("📈 性能统计")
    print("="*60)
    
    print(f"首次加载耗时: {times[0]:.3f}秒")
    if len(times) > 1:
        avg_cache_time = sum(times[1:]) / len(times[1:])
        print(f"缓存命中平均耗时: {avg_cache_time:.3f}秒")
        print(f"性能提升: {times[0] / avg_cache_time:.1f}倍")
    
    print(f"总耗时: {sum(times):.3f}秒")
    print(f"平均耗时: {sum(times) / len(times):.3f}秒")
    
    return times


def test_routing_performance():
    """测试路由性能"""
    print("\n🧪 路由性能测试")
    print("="*60)
    
    # 创建路由器并配置
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    router.configure(routing_config)
    
    # 测试邮件样本
    test_emails = [
        ("告警通知：NBI ID: 620882 设备故障", "4G网元测试"),
        ("基站异常：NBI ID: 3183385 信号中断", "5G基站测试"),
        ("设备掉电：NBI ID: 3188019 电源故障", "对应5G测试"),
        ("普通邮件内容，没有NBI ID", "普通邮件测试"),
        ("告警通知：NBI ID: 999999 未知设备", "未知ID测试")
    ]
    
    print("📊 测试场景：每个邮件路由1000次")
    print("-"*60)
    
    for content, description in test_emails:
        print(f"\n测试: {description}")
        print(f"内容: {content[:50]}...")
        
        start_time = time.time()
        
        for i in range(1000):
            webhook, route_info = router.route_email(content, description)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # 获取最后一次路由结果
        webhook, route_info = router.route_email(content, description)
        
        print(f"  路由结果: {route_info.get('county', '未找到')} → {route_info.get('method', 'unknown')}")
        print(f"  1000次路由耗时: {elapsed:.3f}秒")
        print(f"  平均单次耗时: {elapsed*1000:.3f}毫秒")
        print(f"  QPS: {1000/elapsed:.0f}")


def main():
    """主测试函数"""
    print("🚀 工参缓存和路由性能测试")
    print("="*60)
    
    try:
        # 测试缓存性能
        cache_times = test_cache_performance()
        
        # 测试路由性能
        test_routing_performance()
        
        print("\n" + "="*60)
        print("🎉 性能测试完成")
        print("="*60)
        
        if len(cache_times) > 1:
            improvement = cache_times[0] / (sum(cache_times[1:]) / len(cache_times[1:]))
            print(f"缓存机制性能提升: {improvement:.1f}倍")
            print("✅ 缓存机制工作正常，避免了重复加载")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
