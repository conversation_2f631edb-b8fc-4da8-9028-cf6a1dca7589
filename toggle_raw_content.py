#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件内容处理模式切换工具 - Linus式简洁实用
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.config_manager import config_manager


def show_current_config():
    """显示当前配置"""
    content_config = config_manager.get_content_processing_config()
    
    print("📧 当前内容处理配置:")
    mode = "原文模式" if content_config['raw_content'] else "清理模式"
    print(f"   处理模式: {mode}")
    print(f"   最大长度: {content_config['max_length']}字符")
    
    print("\n💡 模式说明:")
    print("   原文模式: 邮件内容原样输出，保留所有格式和重复内容")
    print("   清理模式: 自动清理HTML标签、去重、格式化")


def toggle_mode():
    """切换处理模式"""
    content_config = config_manager.get_content_processing_config()
    current_raw = content_config['raw_content']
    
    # 切换模式
    new_raw = not current_raw
    content_config['raw_content'] = new_raw
    
    # 保存配置
    config_manager.set_content_processing_config(content_config)
    
    old_mode = "原文模式" if current_raw else "清理模式"
    new_mode = "原文模式" if new_raw else "清理模式"
    
    print(f"✅ 已从 {old_mode} 切换到 {new_mode}")


def set_max_length(new_length: int):
    """设置最大内容长度"""
    if new_length < 100:
        print("❌ 最大长度不能少于100字符")
        return False
    
    if new_length > 10000:
        print("❌ 最大长度不能超过10000字符（钉钉限制）")
        return False
    
    content_config = config_manager.get_content_processing_config()
    content_config['max_length'] = new_length
    
    config_manager.set_content_processing_config(content_config)
    print(f"✅ 最大内容长度已设置为: {new_length}字符")
    return True


def interactive_config():
    """交互式配置"""
    print("=" * 50)
    print("📧 邮件内容处理配置工具")
    print("=" * 50)
    
    show_current_config()
    
    while True:
        print(f"\n请选择操作:")
        print("1. 切换处理模式 (原文 ↔ 清理)")
        print("2. 设置最大长度")
        print("3. 查看当前配置")
        print("4. 退出")
        
        try:
            choice = input(">>> ").strip()
            
            if choice == '1':
                toggle_mode()
                print("\n📊 更新后的配置:")
                show_current_config()
                print(f"\n🔄 请重启程序以应用新配置")
                
            elif choice == '2':
                print("请输入新的最大长度 (100-10000):")
                length_input = input(">>> ").strip()
                try:
                    new_length = int(length_input)
                    if set_max_length(new_length):
                        print("\n📊 更新后的配置:")
                        show_current_config()
                        print(f"\n🔄 请重启程序以应用新配置")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif choice == '3':
                show_current_config()
                
            elif choice == '4' or choice.lower() in ['q', 'quit', 'exit']:
                print("👋 配置完成")
                break
                
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n👋 配置已取消")
            break


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1].lower()
        
        if command == 'toggle':
            show_current_config()
            print()
            toggle_mode()
            print("\n📊 更新后的配置:")
            show_current_config()
            
        elif command == 'raw':
            content_config = config_manager.get_content_processing_config()
            content_config['raw_content'] = True
            config_manager.set_content_processing_config(content_config)
            print("✅ 已切换到原文模式")
            
        elif command == 'clean':
            content_config = config_manager.get_content_processing_config()
            content_config['raw_content'] = False
            config_manager.set_content_processing_config(content_config)
            print("✅ 已切换到清理模式")
            
        elif command == 'status':
            show_current_config()
            
        else:
            print("用法:")
            print("  python toggle_raw_content.py toggle  # 切换模式")
            print("  python toggle_raw_content.py raw     # 设置为原文模式")
            print("  python toggle_raw_content.py clean   # 设置为清理模式")
            print("  python toggle_raw_content.py status  # 查看当前配置")
            print("  python toggle_raw_content.py         # 交互模式")
    else:
        # 交互模式
        interactive_config()


if __name__ == "__main__":
    main()
