#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
磁盘缓存功能测试脚本
"""

import os
import sys
import time
import shutil

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from routing.workparam_router import WorkParamRouter
from config.config_manager import config_manager
from utils.logger import setup_logger, get_logger

# 初始化日志
setup_logger()
logger = get_logger(__name__)


def test_disk_cache_performance():
    """测试磁盘缓存性能"""
    print("🧪 磁盘缓存性能测试")
    print("="*60)
    
    # 清除现有缓存
    cache_dir = os.path.join(".", "cache")
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print("🧹 已清除现有缓存")
    
    # 创建路由器
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    
    print("\n📊 测试场景：首次加载 vs 缓存加载")
    print("-"*60)
    
    # 第一次加载（从Excel文件）
    print("第1次加载（从Excel文件）...")
    start_time = time.time()
    success1 = router.configure(routing_config)
    time1 = time.time() - start_time
    
    if success1:
        stats = router.get_stats()
        print(f"  ✅ 首次加载成功，耗时: {time1:.3f}秒，工参记录: {stats['workparam_records']}条")
    else:
        print(f"  ❌ 首次加载失败，耗时: {time1:.3f}秒")
        return False
    
    # 模拟程序重启 - 创建新的路由器实例
    print("\n🔄 模拟程序重启...")
    router2 = WorkParamRouter(".")
    
    # 第二次加载（从磁盘缓存）
    print("第2次加载（从磁盘缓存）...")
    start_time = time.time()
    success2 = router2.configure(routing_config)
    time2 = time.time() - start_time
    
    if success2:
        stats2 = router2.get_stats()
        print(f"  ✅ 缓存加载成功，耗时: {time2:.3f}秒，工参记录: {stats2['workparam_records']}条")
    else:
        print(f"  ❌ 缓存加载失败，耗时: {time2:.3f}秒")
        return False
    
    # 性能对比
    print("\n" + "="*60)
    print("📈 性能对比")
    print("="*60)
    
    print(f"首次加载（Excel）: {time1:.3f}秒")
    print(f"缓存加载（磁盘）: {time2:.3f}秒")
    
    if time2 > 0:
        improvement = time1 / time2
        print(f"性能提升: {improvement:.1f}倍")
        
        if improvement > 5:
            print("🎉 磁盘缓存效果显著！")
        elif improvement > 2:
            print("✅ 磁盘缓存有效果")
        else:
            print("⚠️ 磁盘缓存效果不明显")
    
    return True


def test_cache_validation():
    """测试缓存验证功能"""
    print("\n🧪 缓存验证测试")
    print("="*60)
    
    # 创建路由器并加载数据
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    
    print("1. 正常加载数据...")
    success = router.configure(routing_config)
    if not success:
        print("❌ 数据加载失败")
        return False
    
    print("✅ 数据加载成功")
    
    # 检查缓存文件
    cache_dir = os.path.join(".", "cache")
    cache_file = os.path.join(cache_dir, "workparam_cache.pkl")
    meta_file = os.path.join(cache_dir, "workparam_meta.json")
    
    print(f"2. 检查缓存文件...")
    print(f"   缓存数据: {'存在' if os.path.exists(cache_file) else '不存在'}")
    print(f"   缓存元数据: {'存在' if os.path.exists(meta_file) else '不存在'}")
    
    if os.path.exists(meta_file):
        import json
        with open(meta_file, 'r', encoding='utf-8') as f:
            meta = json.load(f)
        print(f"   缓存记录数: {meta.get('total_records', 0)}")
        print(f"   缓存时间: {meta.get('cache_time', 'unknown')}")
    
    # 测试缓存重用
    print("\n3. 测试缓存重用...")
    router2 = WorkParamRouter(".")
    start_time = time.time()
    success2 = router2.configure(routing_config)
    cache_time = time.time() - start_time
    
    if success2:
        print(f"✅ 缓存重用成功，耗时: {cache_time:.3f}秒")
    else:
        print(f"❌ 缓存重用失败")
        return False
    
    return True


def test_routing_with_cache():
    """测试缓存加载后的路由功能"""
    print("\n🧪 缓存路由功能测试")
    print("="*60)
    
    # 使用缓存加载数据
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    router.configure(routing_config)
    
    # 测试路由功能
    test_emails = [
        ("告警通知：NBI ID: 620882 设备故障", "4G网元测试"),
        ("基站异常：NBI ID: 3183385 信号中断", "5G基站测试"),
        ("设备掉电：NBI ID: 3188019 电源故障", "对应5G测试")
    ]
    
    print("测试路由功能...")
    for content, description in test_emails:
        webhook, route_info = router.route_email(content, description)
        
        print(f"📧 {description}")
        print(f"   NBI ID: {route_info.get('nbi_id', '未提取')}")
        print(f"   区县: {route_info.get('county', '未找到')}")
        print(f"   路由方式: {route_info.get('method', 'unknown')}")
        print(f"   成功: {'✅' if route_info.get('success') else '❌'}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 磁盘缓存功能测试")
    print("="*60)
    
    test_results = []
    
    try:
        # 运行测试
        test_results.append(("磁盘缓存性能", test_disk_cache_performance()))
        test_results.append(("缓存验证功能", test_cache_validation()))
        test_results.append(("缓存路由功能", test_routing_with_cache()))
        
        # 汇总结果
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:15} : {status}")
            if result:
                passed += 1
        
        print(f"\n总体结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 磁盘缓存功能测试通过！")
            print("\n📋 使用说明:")
            print("1. 首次启动会从Excel加载数据并保存到磁盘缓存")
            print("2. 后续启动会直接从磁盘缓存加载，大幅提升启动速度")
            print("3. 工参文件更新时会自动重新加载并更新缓存")
            print("4. 缓存文件位置: ./cache/workparam_cache.pkl")
            return True
        else:
            print("⚠️ 磁盘缓存功能测试失败，请检查实现。")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
