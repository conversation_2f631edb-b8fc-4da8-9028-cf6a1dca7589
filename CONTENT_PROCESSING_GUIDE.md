# 邮件内容处理指南 - 原文 vs 清理模式

## 用户需求

> "不要清理和去重，原文啥就是啥"

## 解决方案

按照 Linus 的"用户至上"原则，我们提供了两种内容处理模式，默认使用**原文模式**。

## 📧 两种处理模式

### 1. 原文模式 (当前默认) ✅

**特点**:
- ✅ 完全保留邮件原始内容
- ✅ 保留所有HTML标签、格式、重复内容
- ✅ 不做任何清理和去重
- ✅ 用户看到的就是邮件的真实内容

**示例输出**:
```
<html><body>
<p>告警通知：</p>
<p>告警通知：</p>  <!-- 重复内容保留 -->
<div style="color:red">系统异常</div>
<br><br><br>  <!-- 多余空行保留 -->
&nbsp;&nbsp;&nbsp;详细信息...  <!-- HTML实体保留 -->
</body></html>
```

### 2. 清理模式 (可选)

**特点**:
- 🧹 自动清理HTML标签
- 🧹 去除重复内容和空行
- 🧹 格式化输出
- 🧹 移除HTML实体

**示例输出**:
```
告警通知：
系统异常
详细信息...
```

## 🔧 配置方法

### 快速切换

```bash
# 设置为原文模式 (推荐)
python toggle_raw_content.py raw

# 设置为清理模式
python toggle_raw_content.py clean

# 查看当前配置
python toggle_raw_content.py status

# 切换模式
python toggle_raw_content.py toggle
```

### 交互式配置

```bash
# 运行交互式配置工具
python toggle_raw_content.py
```

### 手动配置

编辑 `config/config.json`:
```json
{
  "content_processing": {
    "raw_content": true,    // true=原文模式, false=清理模式
    "max_length": 4000     // 最大内容长度
  }
}
```

## 📊 当前配置状态

```
📧 当前内容处理配置:
   处理模式: 原文模式  ✅
   最大长度: 4000字符
```

## 🎯 推荐设置

### 对于告警邮件 (推荐原文模式)

**优势**:
- **完整信息**: 不会丢失任何重要的告警细节
- **格式保留**: 保持邮件原有的格式和结构
- **无误判**: 程序不会错误地删除"重要"信息
- **真实性**: 看到的就是发件人发送的真实内容

**适用场景**:
- 告警通知邮件
- 重要业务邮件
- 需要保留完整格式的邮件

### 对于普通邮件 (可选清理模式)

**优势**:
- **简洁输出**: 去除HTML标签，更易阅读
- **去重处理**: 减少重复信息干扰
- **格式统一**: 统一的文本格式

**适用场景**:
- 新闻邮件
- 营销邮件
- 格式复杂的HTML邮件

## 🔄 切换示例

### 当前设置 (原文模式)
```bash
$ python toggle_raw_content.py status
📧 当前内容处理配置:
   处理模式: 原文模式
   最大长度: 4000字符
```

### 如需切换到清理模式
```bash
$ python toggle_raw_content.py clean
✅ 已切换到清理模式

$ python toggle_raw_content.py status
📧 当前内容处理配置:
   处理模式: 清理模式
   最大长度: 4000字符
```

## 📏 长度限制

- **默认限制**: 4000字符 (比之前的1500字符更宽松)
- **可调范围**: 100-10000字符
- **超长处理**: 自动截断并添加"...(内容过长，已截断)"提示

### 调整长度限制
```bash
# 交互式调整
python toggle_raw_content.py
# 选择 "2. 设置最大长度"

# 或直接编辑配置文件
"max_length": 6000  // 设置为6000字符
```

## 🚀 技术实现

### Linus式设计原则

1. **"用户说了算"** - 用户要原文就给原文，不自作聪明
2. **"提供选择"** - 支持两种模式，用户可以随时切换
3. **"向后兼容"** - 保留原有的清理功能，不破坏现有配置

### 代码实现
```python
def _format_as_text(self, title: str, content: str, sender: str, time_str: str) -> str:
    content_config = config_manager.get_content_processing_config()
    raw_content = content_config.get("raw_content", True)
    
    if raw_content:
        # 原文模式：用户要求原文啥就是啥
        return content
    else:
        # 清理模式：使用原来的清理逻辑
        return self._clean_content_deprecated(content)
```

## 📋 总结

**当前状态**: 
- ✅ **原文模式已启用** - 邮件内容完全按原文输出
- ✅ **长度限制4000字符** - 比之前更宽松
- ✅ **可随时切换** - 使用配置工具轻松切换模式
- ✅ **向后兼容** - 保留清理功能供需要时使用

**用户体验**:
- 🎯 **所见即所得** - 推送的内容就是邮件的真实内容
- 🔧 **灵活配置** - 可根据需要随时调整
- 🚀 **简单操作** - 一条命令即可切换模式

现在你的程序会完全按照邮件原文推送内容，不会做任何"聪明"的处理。正如 Linus 所说：**"程序应该做用户要求的事，而不是程序认为用户需要的事。"**
