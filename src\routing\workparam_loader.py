#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工参Excel读取器 - Linus式设计
核心原则：一次读取，内存缓存，O(1)查找
"""

import os
import pandas as pd
from datetime import datetime
from typing import Dict, Set, Optional, Tuple
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WorkParamLoader:
    """工参数据加载器 - Linus式简洁设计
    
    核心原则：
    1. 单一职责：只负责加载Excel并构建查找表
    2. 简单数据结构：扁平的字典映射
    3. 消除特殊情况：统一的查找逻辑
    """
    
    def __init__(self, config_dir: str = "."):
        """初始化工参加载器
        
        Args:
            config_dir: 配置目录，工参文件应在此目录下
        """
        self.config_dir = config_dir
        
        # 核心数据结构：NBI ID -> 区县的映射表
        self.nbi_to_county: Dict[str, str] = {}
        
        # 文件状态跟踪
        self.last_load_time: Optional[datetime] = None
        self.file_timestamps: Dict[str, float] = {}
        
        logger.info("📊 工参加载器初始化完成")
    
    def load_workparams(self, file_4g: str, file_5g: str) -> bool:
        """加载工参文件 - 核心功能
        
        Args:
            file_4g: 4G工参文件路径
            file_5g: 5G工参文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            # 构建完整路径
            path_4g = os.path.join(self.config_dir, file_4g)
            path_5g = os.path.join(self.config_dir, file_5g)
            
            # 检查文件存在性
            if not os.path.exists(path_4g):
                logger.error(f"❌ 4G工参文件不存在: {path_4g}")
                return False
                
            if not os.path.exists(path_5g):
                logger.error(f"❌ 5G工参文件不存在: {path_5g}")
                return False
            
            logger.info(f"📊 开始加载工参文件...")
            logger.info(f"   4G工参: {file_4g}")
            logger.info(f"   5G工参: {file_5g}")
            
            # 清空现有数据
            self.nbi_to_county.clear()
            
            # 加载4G工参
            count_4g = self._load_4g_workparam(path_4g)
            
            # 加载5G工参
            count_5g = self._load_5g_workparam(path_5g)
            
            # 更新加载时间和文件时间戳
            self.last_load_time = datetime.now()
            self.file_timestamps[path_4g] = os.path.getmtime(path_4g)
            self.file_timestamps[path_5g] = os.path.getmtime(path_5g)
            
            total_count = len(self.nbi_to_county)
            logger.info(f"✅ 工参加载完成: 4G({count_4g}) + 5G({count_5g}) = 总计{total_count}条记录")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 工参加载失败: {e}")
            return False
    
    def _load_4g_workparam(self, file_path: str) -> int:
        """加载4G工参数据
        
        Args:
            file_path: 4G工参文件路径
            
        Returns:
            int: 加载的记录数
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"📊 4G工参文件读取成功，共{len(df)}行")
            
            # 检查必要的列
            required_columns = ['网元ID', '归属维护网格', '对应5G网元号']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"❌ 4G工参缺少必要列: {missing_columns}")
                return 0
            
            count = 0
            for _, row in df.iterrows():
                try:
                    # 提取关键字段
                    nbi_id = str(row['网元ID']).strip()
                    county = str(row['归属维护网格']).strip()
                    corresponding_5g = str(row['对应5G网元号']).strip()
                    
                    # 跳过空值
                    if not nbi_id or nbi_id == 'nan' or not county or county == 'nan':
                        continue
                    
                    # 添加到映射表：网元ID -> 区县
                    self.nbi_to_county[nbi_id] = county
                    count += 1
                    
                    # 如果有对应5G网元号，也添加映射
                    if corresponding_5g and corresponding_5g != 'nan':
                        self.nbi_to_county[corresponding_5g] = county
                        count += 1
                    
                except Exception as e:
                    logger.debug(f"跳过4G工参行: {e}")
                    continue
            
            logger.info(f"📊 4G工参处理完成，有效记录: {count}条")
            return count
            
        except Exception as e:
            logger.error(f"❌ 4G工参加载失败: {e}")
            return 0
    
    def _load_5g_workparam(self, file_path: str) -> int:
        """加载5G工参数据
        
        Args:
            file_path: 5G工参文件路径
            
        Returns:
            int: 加载的记录数
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"📊 5G工参文件读取成功，共{len(df)}行")
            
            # 检查必要的列
            required_columns = ['基站ID', '归属维护网格']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"❌ 5G工参缺少必要列: {missing_columns}")
                return 0
            
            count = 0
            for _, row in df.iterrows():
                try:
                    # 提取关键字段
                    nbi_id = str(row['基站ID']).strip()
                    county = str(row['归属维护网格']).strip()
                    
                    # 跳过空值
                    if not nbi_id or nbi_id == 'nan' or not county or county == 'nan':
                        continue
                    
                    # 添加到映射表：基站ID -> 区县
                    self.nbi_to_county[nbi_id] = county
                    count += 1
                    
                except Exception as e:
                    logger.debug(f"跳过5G工参行: {e}")
                    continue
            
            logger.info(f"📊 5G工参处理完成，有效记录: {count}条")
            return count
            
        except Exception as e:
            logger.error(f"❌ 5G工参加载失败: {e}")
            return 0
    
    def get_county_by_nbi_id(self, nbi_id: str) -> Optional[str]:
        """根据NBI ID获取区县 - 核心查找功能
        
        Args:
            nbi_id: NBI ID
            
        Returns:
            Optional[str]: 区县名称，未找到返回None
        """
        if not nbi_id:
            return None
            
        # Linus式简洁：O(1)查找，无特殊情况
        return self.nbi_to_county.get(str(nbi_id).strip())
    
    def is_loaded(self) -> bool:
        """检查是否已加载数据"""
        return len(self.nbi_to_county) > 0
    
    def get_stats(self) -> Dict[str, any]:
        """获取加载统计信息"""
        return {
            'total_records': len(self.nbi_to_county),
            'last_load_time': self.last_load_time,
            'is_loaded': self.is_loaded()
        }
    
    def need_reload(self, file_4g: str, file_5g: str) -> bool:
        """检查是否需要重新加载
        
        Args:
            file_4g: 4G工参文件路径
            file_5g: 5G工参文件路径
            
        Returns:
            bool: 是否需要重新加载
        """
        try:
            path_4g = os.path.join(self.config_dir, file_4g)
            path_5g = os.path.join(self.config_dir, file_5g)
            
            # 检查文件是否存在
            if not os.path.exists(path_4g) or not os.path.exists(path_5g):
                return False
            
            # 检查文件时间戳是否变化
            current_4g_time = os.path.getmtime(path_4g)
            current_5g_time = os.path.getmtime(path_5g)
            
            return (current_4g_time != self.file_timestamps.get(path_4g) or
                    current_5g_time != self.file_timestamps.get(path_5g))
                    
        except Exception as e:
            logger.debug(f"检查重新加载状态失败: {e}")
            return False
