#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NBI ID匹配逻辑测试脚本
验证邮件中的NBI ID与工参的正确匹配
"""

import os
import sys
import pandas as pd

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from routing.workparam_loader import WorkParamLoader
from routing.nbi_extractor import NBIExtractor
from routing.workparam_router import WorkParamRouter
from utils.logger import setup_logger, get_logger

# 初始化日志
setup_logger()
logger = get_logger(__name__)


def test_real_workparam_data():
    """测试真实工参数据的匹配"""
    print("\n" + "="*60)
    print("🧪 测试真实工参数据匹配")
    print("="*60)
    
    # 先查看工参文件中的实际数据
    print("\n📊 查看4G工参数据样本:")
    try:
        df_4g = pd.read_excel("菏泽联通4G现网工参-2025-08-18.xlsx", nrows=10)
        print("4G工参列名:", list(df_4g.columns))
        
        # 显示前几行的关键字段
        if '网元ID' in df_4g.columns and '归属维护网格' in df_4g.columns:
            for i, row in df_4g.head(5).iterrows():
                network_id = str(row['网元ID']).strip()
                county = str(row['归属维护网格']).strip()
                corresponding_5g = str(row.get('对应5G网元号', 'N/A')).strip()
                print(f"  网元ID: {network_id} → 区县: {county} → 对应5G: {corresponding_5g}")
        
    except Exception as e:
        print(f"❌ 读取4G工参失败: {e}")
    
    print("\n📊 查看5G工参数据样本:")
    try:
        df_5g = pd.read_excel("菏泽联通5G-现网工参-2025-08-18.xlsx", nrows=10)
        print("5G工参列名:", list(df_5g.columns))
        
        # 显示前几行的关键字段
        if '基站ID' in df_5g.columns and '归属维护网格' in df_5g.columns:
            for i, row in df_5g.head(5).iterrows():
                base_id = str(row['基站ID']).strip()
                county = str(row['归属维护网格']).strip()
                print(f"  基站ID: {base_id} → 区县: {county}")
        
    except Exception as e:
        print(f"❌ 读取5G工参失败: {e}")


def test_nbi_matching_priority():
    """测试NBI ID匹配优先级"""
    print("\n" + "="*60)
    print("🧪 测试NBI ID匹配优先级")
    print("="*60)
    
    # 加载工参数据
    loader = WorkParamLoader(".")
    success = loader.load_workparams(
        "菏泽联通4G现网工参-2025-08-18.xlsx",
        "菏泽联通5G-现网工参-2025-08-18.xlsx"
    )
    
    if not success:
        print("❌ 工参加载失败")
        return False
    
    # 显示加载统计
    stats = loader.get_stats()
    print(f"\n📊 工参加载统计:")
    print(f"  4G网元ID: {stats['g4_network_id_count']}条")
    print(f"  5G基站ID: {stats['g5_base_id_count']}条")
    print(f"  4G对应5G: {stats['g4_corresponding_5g_count']}条")
    print(f"  总计: {stats['total_records']}条")
    
    # 获取一些真实的ID进行测试
    print(f"\n🎯 测试真实ID匹配:")
    
    # 测试4G网元ID
    if loader.g4_network_id_to_county:
        test_4g_id = list(loader.g4_network_id_to_county.keys())[0]
        county = loader.get_county_by_nbi_id(test_4g_id)
        print(f"  4G网元ID测试: {test_4g_id} → {county}")
    
    # 测试5G基站ID
    if loader.g5_base_id_to_county:
        test_5g_id = list(loader.g5_base_id_to_county.keys())[0]
        county = loader.get_county_by_nbi_id(test_5g_id)
        print(f"  5G基站ID测试: {test_5g_id} → {county}")
    
    # 测试4G对应5G
    if loader.g4_corresponding_5g_to_county:
        test_corresponding_id = list(loader.g4_corresponding_5g_to_county.keys())[0]
        county = loader.get_county_by_nbi_id(test_corresponding_id)
        print(f"  4G对应5G测试: {test_corresponding_id} → {county}")
    
    return True


def test_nbi_extraction():
    """测试NBI ID提取"""
    print("\n" + "="*60)
    print("🧪 测试NBI ID提取")
    print("="*60)
    
    extractor = NBIExtractor()
    
    # 测试各种NBI ID格式
    test_cases = [
        "告警通知：NBI ID: HZ001234 设备故障",
        "网络异常 NBI ID：ABC123_456 请处理",
        "紧急告警 网元ID: ******** 信号中断",
        "基站告警 基站ID：********* 覆盖异常",
        "设备离线 网元编号：********* 立即检查",
        "普通邮件内容，没有任何ID信息"
    ]
    
    for i, content in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {content}")
        nbi_id = extractor.extract_nbi_id(content)
        if nbi_id:
            print(f"  ✅ 提取到NBI ID: {nbi_id}")
        else:
            print(f"  ❌ 未提取到NBI ID")


def test_end_to_end_routing():
    """测试端到端路由"""
    print("\n" + "="*60)
    print("🧪 测试端到端路由")
    print("="*60)
    
    # 初始化路由器
    router = WorkParamRouter(".")
    
    # 配置路由器
    routing_config = {
        "enabled": True,
        "nbi_extraction": {
            "enabled": True,
            "patterns": []
        },
        "workparam_files": {
            "4g_file": "菏泽联通4G现网工参-2025-08-18.xlsx",
            "5g_file": "菏泽联通5G-现网工参-2025-08-18.xlsx"
        },
        "county_webhooks": {
            "牡丹区": "https://webhook.example.com/mudan",
            "定陶区": "https://webhook.example.com/dingtao",
            "曹县": "https://webhook.example.com/caoxian",
            "单县": "https://webhook.example.com/danxian",
            "成武县": "https://webhook.example.com/chengwu",
            "巨野县": "https://webhook.example.com/juye",
            "郓城县": "https://webhook.example.com/yuncheng",
            "鄄城县": "https://webhook.example.com/juancheng",
            "东明县": "https://webhook.example.com/dongming",
            "default": "https://webhook.example.com/default"
        }
    }
    
    success = router.configure(routing_config)
    if not success:
        print("❌ 路由器配置失败")
        return False
    
    # 获取一些真实ID进行端到端测试
    loader = router.workparam_loader
    
    test_emails = []
    
    # 构造测试邮件
    if loader.g4_network_id_to_county:
        real_4g_id = list(loader.g4_network_id_to_county.keys())[0]
        test_emails.append((f"告警通知：NBI ID: {real_4g_id} 设备故障", f"4G网元告警-{real_4g_id}"))
    
    if loader.g5_base_id_to_county:
        real_5g_id = list(loader.g5_base_id_to_county.keys())[0]
        test_emails.append((f"基站异常：NBI ID: {real_5g_id} 信号中断", f"5G基站告警-{real_5g_id}"))
    
    if loader.g4_corresponding_5g_to_county:
        real_corresponding_id = list(loader.g4_corresponding_5g_to_county.keys())[0]
        test_emails.append((f"网络告警：NBI ID: {real_corresponding_id} 覆盖问题", f"对应5G告警-{real_corresponding_id}"))
    
    # 添加未知ID测试
    test_emails.append(("告警：NBI ID: UNKNOWN999 未知设备", "未知设备告警"))
    test_emails.append(("普通邮件内容，没有NBI ID", "普通邮件"))
    
    # 执行路由测试
    for content, subject in test_emails:
        print(f"\n📧 测试邮件: {subject}")
        print(f"   内容: {content}")
        
        webhook, route_info = router.route_email(content, subject)
        
        print(f"   NBI ID: {route_info.get('nbi_id', '未提取')}")
        print(f"   区县: {route_info.get('county', '未找到')}")
        print(f"   Webhook: {webhook or '默认'}")
        print(f"   路由方式: {route_info.get('method', 'unknown')}")
        print(f"   成功: {'✅' if route_info.get('success') else '❌'}")
    
    # 显示路由统计
    stats = router.get_stats()
    print(f"\n📊 路由统计:")
    print(f"   总路由次数: {stats['total_routed']}")
    print(f"   成功路由: {stats['successful_routes']}")
    print(f"   提取失败: {stats['failed_extractions']}")
    print(f"   路由失败: {stats['failed_routes']}")
    print(f"   默认路由: {stats['default_routes']}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始NBI ID匹配逻辑测试")
    print("="*60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("真实工参数据", test_real_workparam_data()))
    test_results.append(("NBI匹配优先级", test_nbi_matching_priority()))
    test_results.append(("NBI ID提取", test_nbi_extraction()))
    test_results.append(("端到端路由", test_end_to_end_routing()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！NBI ID匹配逻辑正确。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
