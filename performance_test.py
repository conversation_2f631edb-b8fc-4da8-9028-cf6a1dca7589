#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 验证优化效果
"""

import time
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.config_manager import config_manager
from src.email_handler.simple_email_client import SimpleEmailClient
from src.core.universal_email_pusher import EmailAlertPusher
from src.pusher.dingtalk_pusher import DingTalkPusher
from src.utils.performance_monitor import performance_monitor
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_email_fetch_performance():
    """测试邮件获取性能"""
    print("🧪 开始性能测试...")
    
    # 获取配置
    email_accounts = config_manager.get_email_accounts()
    if not email_accounts:
        print("❌ 没有配置邮箱账户")
        return
    
    # 创建邮件客户端
    email_clients = []
    for account in email_accounts:
        if account.get('enabled', True):
            client = SimpleEmailClient(account)
            email_clients.append(client)
    
    if not email_clients:
        print("❌ 没有可用的邮件客户端")
        return
    
    print(f"📧 测试邮件客户端数量: {len(email_clients)}")
    
    # 创建推送器（测试用）
    webhooks = config_manager.get_webhooks()
    pusher = DingTalkPusher(webhooks) if webhooks else None
    
    # 创建邮件推送器
    keywords = config_manager.get_keywords()
    email_pusher = EmailAlertPusher(email_clients, pusher, keywords)
    
    # 性能测试
    print("\n🚀 开始性能测试...")
    
    # 测试1: 邮件获取性能
    print("\n📊 测试1: 邮件获取性能")
    start_time = time.time()
    
    for i in range(3):  # 测试3次
        print(f"   第 {i+1} 次测试...")
        emails = email_pusher.fetch_all_emails_fast()
        print(f"   获取到 {len(emails)} 封邮件")
        time.sleep(1)  # 间隔1秒
    
    fetch_duration = time.time() - start_time
    print(f"   总耗时: {fetch_duration:.1f}秒")
    print(f"   平均耗时: {fetch_duration/3:.1f}秒/次")
    
    # 测试2: 完整检查性能
    print("\n📊 测试2: 完整检查性能")
    start_time = time.time()
    
    for i in range(2):  # 测试2次
        print(f"   第 {i+1} 次完整检查...")
        result = email_pusher.check_and_push_new_emails()
        print(f"   检查结果: {'成功' if result else '失败'}")
        time.sleep(2)  # 间隔2秒
    
    check_duration = time.time() - start_time
    print(f"   总耗时: {check_duration:.1f}秒")
    print(f"   平均耗时: {check_duration/2:.1f}秒/次")
    
    # 测试3: 内存使用情况
    print("\n📊 测试3: 内存使用情况")
    memory_stats = email_pusher.get_memory_stats()
    print(f"   已见UID数量: {memory_stats['seen_uids_count']}")
    print(f"   内存使用率: {memory_stats['memory_usage_percent']:.1f}%")
    print(f"   清理阈值: {memory_stats['cleanup_threshold']}")
    
    # 测试4: 性能监控统计
    print("\n📊 测试4: 性能监控统计")
    performance_monitor.log_performance_summary()
    
    overall_stats = performance_monitor.get_overall_stats()
    print(f"   总操作数: {overall_stats['total_operations']}")
    print(f"   错误率: {overall_stats['error_rate']:.1f}%")
    print(f"   操作频率: {overall_stats['operations_per_minute']:.1f}/分钟")
    
    # 输出各操作的详细统计
    for op_name in overall_stats['monitored_operations']:
        stats = performance_monitor.get_operation_stats(op_name)
        if stats:
            print(f"   {op_name}:")
            print(f"     平均耗时: {stats['avg_duration']:.2f}秒")
            print(f"     成功率: {stats['success_rate']:.1f}%")
            print(f"     执行次数: {stats['total_count']}")
    
    print("\n✅ 性能测试完成!")


def test_connection_pool():
    """测试连接池性能"""
    print("\n🔗 测试连接池性能...")
    
    from src.email_handler.connection_pool import connection_pool
    
    # 获取连接池统计
    stats = connection_pool.get_stats()
    print(f"   活跃连接数: {stats['active_connections']}")
    print(f"   连接邮箱: {stats['connection_emails']}")
    print(f"   空闲超时: {stats['max_idle_time']}秒")
    
    # 清理空闲连接
    print("   清理空闲连接...")
    connection_pool.cleanup_idle_connections()


def main():
    """主函数"""
    print("=" * 60)
    print("📊 邮件告警推送程序 - 性能测试")
    print("=" * 60)
    
    try:
        # 邮件获取性能测试
        test_email_fetch_performance()
        
        # 连接池测试
        test_connection_pool()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
