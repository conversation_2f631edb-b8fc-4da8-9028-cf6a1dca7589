#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控器 - <PERSON><PERSON>式简洁设计
核心原则：只监控关键指标，不影响主要性能
"""

import time
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass
from collections import deque
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    duration: float
    timestamp: float
    success: bool
    error_message: Optional[str] = None


class PerformanceMonitor:
    """性能监控器 - Linus式轻量级设计"""
    
    def __init__(self, max_history: int = 100):
        self.metrics: Dict[str, deque] = {}
        self.max_history = max_history
        self._lock = threading.RLock()
        
        # 统计数据
        self.total_operations = 0
        self.total_errors = 0
        self.start_time = time.time()
        
        logger.info(f"📊 性能监控器启动，历史记录上限: {max_history}")
    
    def record_operation(self, operation_name: str, duration: float, 
                        success: bool = True, error_message: str = None):
        """记录操作性能"""
        with self._lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = deque(maxlen=self.max_history)
            
            metric = PerformanceMetric(
                name=operation_name,
                duration=duration,
                timestamp=time.time(),
                success=success,
                error_message=error_message
            )
            
            self.metrics[operation_name].append(metric)
            self.total_operations += 1
            
            if not success:
                self.total_errors += 1
    
    def get_operation_stats(self, operation_name: str) -> Dict:
        """获取操作统计信息"""
        with self._lock:
            if operation_name not in self.metrics:
                return {}
            
            metrics = list(self.metrics[operation_name])
            if not metrics:
                return {}
            
            durations = [m.duration for m in metrics]
            successes = [m for m in metrics if m.success]
            errors = [m for m in metrics if not m.success]
            
            return {
                'operation': operation_name,
                'total_count': len(metrics),
                'success_count': len(successes),
                'error_count': len(errors),
                'success_rate': len(successes) / len(metrics) * 100,
                'avg_duration': sum(durations) / len(durations),
                'min_duration': min(durations),
                'max_duration': max(durations),
                'recent_errors': [e.error_message for e in errors[-5:]]  # 最近5个错误
            }
    
    def get_overall_stats(self) -> Dict:
        """获取整体统计信息"""
        with self._lock:
            uptime = time.time() - self.start_time
            
            return {
                'uptime_seconds': uptime,
                'total_operations': self.total_operations,
                'total_errors': self.total_errors,
                'error_rate': (self.total_errors / max(self.total_operations, 1)) * 100,
                'operations_per_minute': (self.total_operations / max(uptime / 60, 1)),
                'monitored_operations': list(self.metrics.keys())
            }
    
    def log_performance_summary(self):
        """输出性能摘要日志"""
        overall = self.get_overall_stats()
        
        logger.info(f"📊 性能摘要:")
        logger.info(f"   运行时间: {overall['uptime_seconds']:.1f}秒")
        logger.info(f"   总操作数: {overall['total_operations']}")
        logger.info(f"   错误率: {overall['error_rate']:.1f}%")
        logger.info(f"   操作频率: {overall['operations_per_minute']:.1f}/分钟")
        
        # 输出各操作的详细统计
        for op_name in overall['monitored_operations']:
            stats = self.get_operation_stats(op_name)
            if stats:
                logger.info(f"   {op_name}: 平均{stats['avg_duration']:.2f}秒, "
                          f"成功率{stats['success_rate']:.1f}%")


class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, monitor: PerformanceMonitor, operation_name: str):
        self.monitor = monitor
        self.operation_name = operation_name
        self.start_time = None
        self.error_message = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        success = exc_type is None
        error_message = str(exc_val) if exc_val else None
        
        self.monitor.record_operation(
            self.operation_name, 
            duration, 
            success, 
            error_message
        )
        
        # 如果操作时间过长，记录警告
        if duration > 10:  # 超过10秒
            logger.warning(f"⚠️ 操作耗时过长: {self.operation_name} - {duration:.1f}秒")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def monitor_performance(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(performance_monitor, operation_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def log_performance_if_slow(operation_name: str, threshold: float = 5.0):
    """如果操作慢于阈值则记录日志的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                if duration > threshold:
                    logger.warning(f"🐌 慢操作: {operation_name} - {duration:.1f}秒")
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ 操作失败: {operation_name} - {duration:.1f}秒 - {e}")
                raise
        return wrapper
    return decorator
