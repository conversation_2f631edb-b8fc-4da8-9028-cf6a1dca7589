#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NBI ID提取器 - Linus式设计
核心原则：简单正则，容错处理，优雅降级
"""

import re
from typing import List, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class NBIExtractor:
    """NBI ID提取器 - Linus式简洁设计
    
    核心原则：
    1. 单一职责：只负责从文本中提取NBI ID
    2. 简单可靠：使用正则表达式模式匹配
    3. 容错处理：提取失败时优雅降级
    """
    
    def __init__(self, patterns: List[str] = None):
        """初始化NBI ID提取器
        
        Args:
            patterns: 正则表达式模式列表，用于匹配NBI ID
        """
        # 默认的NBI ID匹配模式
        self.default_patterns = [
            r'NBI\s*ID\s*[：:]\s*(\w+)',           # NBI ID: 123456
            r'网元编号\s*[：:]\s*(\w+)',            # 网元编号：123456
            r'基站ID\s*[：:]\s*(\w+)',             # 基站ID: 123456
            r'网元ID\s*[：:]\s*(\w+)',             # 网元ID: 123456
            r'CELLID\s*[：:]\s*(\w+)',            # CELLID: 123456
            r'小区ID\s*[：:]\s*(\w+)',             # 小区ID: 123456
            r'站点编号\s*[：:]\s*(\w+)',            # 站点编号: 123456
            r'设备编号\s*[：:]\s*(\w+)',            # 设备编号: 123456
        ]
        
        # 使用自定义模式或默认模式
        self.patterns = patterns if patterns else self.default_patterns
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = []
        for pattern in self.patterns:
            try:
                compiled = re.compile(pattern, re.IGNORECASE | re.MULTILINE)
                self.compiled_patterns.append(compiled)
                logger.debug(f"✅ 编译正则模式: {pattern}")
            except re.error as e:
                logger.warning(f"❌ 正则模式编译失败: {pattern}, 错误: {e}")
        
        logger.info(f"📝 NBI提取器初始化完成，有效模式: {len(self.compiled_patterns)}个")
    
    def extract_nbi_id(self, content: str) -> Optional[str]:
        """从邮件内容中提取NBI ID - 核心功能
        
        Args:
            content: 邮件内容
            
        Returns:
            Optional[str]: 提取到的NBI ID，未找到返回None
        """
        if not content:
            logger.debug("邮件内容为空，无法提取NBI ID")
            return None
        
        # 清理内容：移除多余的空白字符
        cleaned_content = re.sub(r'\s+', ' ', content.strip())
        
        # 按优先级尝试每个模式
        for i, pattern in enumerate(self.compiled_patterns):
            try:
                match = pattern.search(cleaned_content)
                if match:
                    nbi_id = match.group(1).strip()
                    if nbi_id:
                        logger.info(f"✅ 提取到NBI ID: {nbi_id} (使用模式{i+1})")
                        return nbi_id
                        
            except Exception as e:
                logger.debug(f"模式{i+1}匹配失败: {e}")
                continue
        
        # 所有模式都失败，记录调试信息
        logger.debug("❌ 未能提取到NBI ID")
        logger.debug(f"邮件内容前200字符: {cleaned_content[:200]}...")
        
        return None
    
    def extract_all_nbi_ids(self, content: str) -> List[str]:
        """从邮件内容中提取所有NBI ID
        
        Args:
            content: 邮件内容
            
        Returns:
            List[str]: 提取到的所有NBI ID列表
        """
        if not content:
            return []
        
        all_nbi_ids = []
        cleaned_content = re.sub(r'\s+', ' ', content.strip())
        
        # 使用每个模式查找所有匹配
        for i, pattern in enumerate(self.compiled_patterns):
            try:
                matches = pattern.findall(cleaned_content)
                for match in matches:
                    nbi_id = match.strip() if isinstance(match, str) else match[0].strip()
                    if nbi_id and nbi_id not in all_nbi_ids:
                        all_nbi_ids.append(nbi_id)
                        logger.debug(f"找到NBI ID: {nbi_id} (模式{i+1})")
                        
            except Exception as e:
                logger.debug(f"模式{i+1}批量匹配失败: {e}")
                continue
        
        if all_nbi_ids:
            logger.info(f"✅ 提取到{len(all_nbi_ids)}个NBI ID: {all_nbi_ids}")
        else:
            logger.debug("❌ 未能提取到任何NBI ID")
        
        return all_nbi_ids
    
    def add_pattern(self, pattern: str) -> bool:
        """添加新的匹配模式
        
        Args:
            pattern: 正则表达式模式
            
        Returns:
            bool: 是否添加成功
        """
        try:
            compiled = re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            self.patterns.append(pattern)
            self.compiled_patterns.append(compiled)
            logger.info(f"✅ 添加新模式: {pattern}")
            return True
            
        except re.error as e:
            logger.error(f"❌ 模式添加失败: {pattern}, 错误: {e}")
            return False
    
    def get_patterns(self) -> List[str]:
        """获取当前所有模式"""
        return self.patterns.copy()
    
    def test_pattern(self, pattern: str, test_content: str) -> List[str]:
        """测试正则模式
        
        Args:
            pattern: 要测试的正则模式
            test_content: 测试内容
            
        Returns:
            List[str]: 匹配结果列表
        """
        try:
            compiled = re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            matches = compiled.findall(test_content)
            results = []
            
            for match in matches:
                if isinstance(match, str):
                    results.append(match.strip())
                elif isinstance(match, tuple):
                    results.append(match[0].strip())
            
            logger.info(f"🧪 模式测试: {pattern}")
            logger.info(f"   匹配结果: {results}")
            
            return results
            
        except re.error as e:
            logger.error(f"❌ 模式测试失败: {pattern}, 错误: {e}")
            return []
