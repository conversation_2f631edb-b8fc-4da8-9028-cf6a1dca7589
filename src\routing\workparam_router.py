#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工参路由器 - Linus式设计
核心原则：NBI ID → 区县 → Webhook，消除所有特殊情况
"""

from typing import Dict, List, Optional, Tuple
from src.routing.workparam_loader import WorkParamLoader
from src.routing.nbi_extractor import NBIExtractor
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WorkParamRouter:
    """工参路由器 - Linus式简洁设计
    
    核心原则：
    1. 单一数据流：NBI ID → 区县 → Webhook
    2. 消除特殊情况：统一的查找和路由逻辑
    3. 优雅降级：找不到时使用默认路由
    """
    
    def __init__(self, config_dir: str = "."):
        """初始化路由器
        
        Args:
            config_dir: 配置目录
        """
        # 核心组件
        self.workparam_loader = WorkParamLoader(config_dir)
        self.nbi_extractor = NBIExtractor()
        
        # 路由配置
        self.county_to_webhook: Dict[str, str] = {}
        self.default_webhook: Optional[str] = None
        self.enabled = False
        
        # 统计信息
        self.stats = {
            'total_routed': 0,
            'successful_routes': 0,
            'failed_extractions': 0,
            'failed_routes': 0,
            'default_routes': 0
        }
        
        logger.info("🚦 工参路由器初始化完成")
    
    def configure(self, routing_config: Dict) -> bool:
        """配置路由器
        
        Args:
            routing_config: 路由配置字典
            
        Returns:
            bool: 配置是否成功
        """
        try:
            # 基本配置
            self.enabled = routing_config.get('enabled', False)
            
            # 配置NBI提取器
            nbi_config = routing_config.get('nbi_extraction', {})
            if nbi_config.get('enabled', True):
                patterns = nbi_config.get('patterns', [])
                if patterns:
                    self.nbi_extractor = NBIExtractor(patterns)
            
            # 配置区县到Webhook的映射
            self.county_to_webhook = routing_config.get('county_webhooks', {})
            self.default_webhook = self.county_to_webhook.get('default')
            
            # 智能加载工参文件 - 只在需要时加载
            workparam_config = routing_config.get('workparam_files', {})
            file_4g = workparam_config.get('4g_file')
            file_5g = workparam_config.get('5g_file')

            if file_4g and file_5g:
                # 检查是否需要加载：首次加载或文件已更新
                if not self.workparam_loader.is_loaded():
                    logger.info("📊 首次加载工参文件...")
                    success = self.workparam_loader.load_workparams(file_4g, file_5g)
                    if not success:
                        logger.warning("⚠️ 工参文件加载失败，路由功能将降级")
                elif self.workparam_loader.need_reload(file_4g, file_5g):
                    logger.info("📊 检测到工参文件更新，重新加载...")
                    success = self.workparam_loader.load_workparams(file_4g, file_5g)
                    if not success:
                        logger.warning("⚠️ 工参文件重新加载失败")
                else:
                    logger.debug("📊 工参文件已缓存，跳过加载")
                    success = True
            
            logger.info(f"🚦 路由器配置完成:")
            logger.info(f"   启用状态: {self.enabled}")
            logger.info(f"   区县映射: {len(self.county_to_webhook)}个")
            stats = self.workparam_loader.get_stats()
            logger.info(f"   工参记录: {stats['total_records']}条")
            logger.info(f"   默认Webhook: {'已配置' if self.default_webhook else '未配置'}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 路由器配置失败: {e}")
            return False
    
    def route_email(self, email_content: str, subject: str = "") -> Tuple[Optional[str], Dict]:
        """路由邮件到对应的Webhook - 核心功能
        
        Args:
            email_content: 邮件内容
            subject: 邮件主题（可选，用于调试）
            
        Returns:
            Tuple[Optional[str], Dict]: (Webhook URL, 路由信息)
        """
        self.stats['total_routed'] += 1
        
        route_info = {
            'nbi_id': None,
            'county': None,
            'webhook': None,
            'method': 'unknown',
            'success': False
        }
        
        try:
            # 检查是否启用路由
            if not self.enabled:
                route_info['method'] = 'disabled'
                return self.default_webhook, route_info
            
            # 步骤1: 提取NBI ID
            nbi_id = self.nbi_extractor.extract_nbi_id(email_content)
            route_info['nbi_id'] = nbi_id
            
            if not nbi_id:
                logger.debug(f"📧 未提取到NBI ID，使用默认路由: {subject[:50]}...")
                self.stats['failed_extractions'] += 1
                route_info['method'] = 'no_nbi_id'
                route_info['webhook'] = self.default_webhook
                self.stats['default_routes'] += 1
                return self.default_webhook, route_info
            
            # 步骤2: 查找区县
            county = self.workparam_loader.get_county_by_nbi_id(nbi_id)
            route_info['county'] = county
            
            if not county:
                logger.debug(f"📧 NBI ID未找到对应区县: {nbi_id}，使用默认路由")
                self.stats['failed_routes'] += 1
                route_info['method'] = 'no_county'
                route_info['webhook'] = self.default_webhook
                self.stats['default_routes'] += 1
                return self.default_webhook, route_info
            
            # 步骤3: 查找Webhook
            webhook = self.county_to_webhook.get(county, self.default_webhook)
            route_info['webhook'] = webhook
            
            if webhook == self.default_webhook:
                logger.debug(f"📧 区县未配置专用Webhook: {county}，使用默认路由")
                route_info['method'] = 'no_webhook_config'
                self.stats['default_routes'] += 1
            else:
                logger.info(f"✅ 路由成功: NBI ID({nbi_id}) → 区县({county}) → Webhook")
                route_info['method'] = 'success'
                route_info['success'] = True
                self.stats['successful_routes'] += 1
            
            return webhook, route_info
            
        except Exception as e:
            logger.error(f"❌ 邮件路由失败: {e}")
            route_info['method'] = 'error'
            route_info['error'] = str(e)
            self.stats['failed_routes'] += 1
            return self.default_webhook, route_info
    
    def batch_route_emails(self, emails: List[Tuple[str, str]]) -> List[Tuple[Optional[str], Dict]]:
        """批量路由邮件
        
        Args:
            emails: 邮件列表，每个元素为(内容, 主题)
            
        Returns:
            List[Tuple[Optional[str], Dict]]: 路由结果列表
        """
        results = []
        for content, subject in emails:
            webhook, route_info = self.route_email(content, subject)
            results.append((webhook, route_info))
        
        logger.info(f"📊 批量路由完成: {len(emails)}封邮件")
        return results
    
    def reload_workparams(self, file_4g: str, file_5g: str) -> bool:
        """重新加载工参文件
        
        Args:
            file_4g: 4G工参文件路径
            file_5g: 5G工参文件路径
            
        Returns:
            bool: 是否重新加载成功
        """
        try:
            logger.info("🔄 开始重新加载工参文件...")
            success = self.workparam_loader.load_workparams(file_4g, file_5g)
            
            if success:
                logger.info("✅ 工参文件重新加载成功")
            else:
                logger.warning("⚠️ 工参文件重新加载失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 重新加载工参文件失败: {e}")
            return False
    
    def check_and_reload_if_needed(self, file_4g: str, file_5g: str) -> bool:
        """检查并在需要时重新加载工参文件
        
        Args:
            file_4g: 4G工参文件路径
            file_5g: 5G工参文件路径
            
        Returns:
            bool: 是否执行了重新加载
        """
        if self.workparam_loader.need_reload(file_4g, file_5g):
            logger.info("📊 检测到工参文件更新，开始重新加载...")
            return self.reload_workparams(file_4g, file_5g)
        return False
    
    def get_stats(self) -> Dict:
        """获取路由统计信息"""
        stats = self.stats.copy()
        workparam_stats = self.workparam_loader.get_stats()
        stats.update({
            'enabled': self.enabled,
            'workparam_records': workparam_stats['total_records'],
            'county_webhooks': len(self.county_to_webhook),
            'has_default_webhook': bool(self.default_webhook),
            'workparam_stats': workparam_stats
        })
        return stats
    
    def test_nbi_extraction(self, test_content: str) -> Dict:
        """测试NBI ID提取功能
        
        Args:
            test_content: 测试内容
            
        Returns:
            Dict: 测试结果
        """
        nbi_id = self.nbi_extractor.extract_nbi_id(test_content)
        all_nbi_ids = self.nbi_extractor.extract_all_nbi_ids(test_content)
        
        result = {
            'content_length': len(test_content),
            'first_nbi_id': nbi_id,
            'all_nbi_ids': all_nbi_ids,
            'extraction_success': bool(nbi_id)
        }
        
        if nbi_id:
            county = self.workparam_loader.get_county_by_nbi_id(nbi_id)
            webhook = self.county_to_webhook.get(county, self.default_webhook) if county else self.default_webhook
            
            result.update({
                'county': county,
                'webhook': webhook,
                'route_success': bool(county)
            })
        
        return result
