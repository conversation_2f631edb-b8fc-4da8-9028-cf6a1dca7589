#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件获取数量配置工具 - <PERSON><PERSON>式简洁实用
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.config_manager import config_manager


def show_current_config():
    """显示当前配置"""
    perf_config = config_manager.get_performance_config()
    
    print("📊 当前性能配置:")
    print(f"   每次获取邮件数量: {perf_config['max_emails_per_fetch']}")
    print(f"   并发超时时间: {perf_config['concurrent_timeout']}秒")
    print(f"   连接超时时间: {perf_config['connection_timeout']}秒")


def update_email_limit(new_limit: int):
    """更新邮件获取数量限制"""
    if new_limit < 10:
        print("❌ 邮件数量不能少于10封")
        return False
    
    if new_limit > 1000:
        print("❌ 邮件数量不能超过1000封（性能考虑）")
        return False
    
    # 获取当前配置
    perf_config = config_manager.get_performance_config()
    
    # 更新邮件数量限制
    perf_config['max_emails_per_fetch'] = new_limit
    
    # 保存配置
    config_manager.set_performance_config(perf_config)
    
    print(f"✅ 邮件获取数量已更新为: {new_limit}封")
    return True


def interactive_config():
    """交互式配置"""
    print("=" * 50)
    print("📧 邮件获取数量配置工具")
    print("=" * 50)
    
    show_current_config()
    
    print("\n💡 建议配置:")
    print("   - 少量邮件 (< 100封): 设置为 100-200")
    print("   - 中等邮件 (100-500封): 设置为 200-300") 
    print("   - 大量邮件 (> 500封): 设置为 300-500")
    print("   - 注意: 数量越大，获取时间越长")
    
    while True:
        try:
            print(f"\n请输入新的邮件获取数量 (10-1000，当前: {config_manager.get_performance_config()['max_emails_per_fetch']}):")
            user_input = input(">>> ").strip()
            
            if user_input.lower() in ['q', 'quit', 'exit', '退出']:
                print("👋 配置已取消")
                break
            
            new_limit = int(user_input)
            
            if update_email_limit(new_limit):
                print("\n📊 更新后的配置:")
                show_current_config()
                
                print(f"\n🔄 请重启程序以应用新配置")
                break
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 配置已取消")
            break


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        try:
            new_limit = int(sys.argv[1])
            if update_email_limit(new_limit):
                show_current_config()
        except ValueError:
            print("❌ 请输入有效的数字")
            print("用法: python update_email_limit.py <数量>")
            print("例如: python update_email_limit.py 300")
    else:
        # 交互模式
        interactive_config()


if __name__ == "__main__":
    main()
