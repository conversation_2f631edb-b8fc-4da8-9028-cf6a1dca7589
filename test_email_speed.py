#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件获取速度测试 - 验证批量优化效果
"""

import time
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.config_manager import config_manager
from src.email_handler.simple_email_client import SimpleEmailClient
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_email_fetch_speed():
    """测试邮件获取速度"""
    print("🚀 邮件获取速度测试")
    print("=" * 50)
    
    # 获取邮件配置
    email_accounts = config_manager.get_email_accounts()
    if not email_accounts:
        print("❌ 没有配置邮件账户")
        return
    
    # 使用第一个启用的账户
    account = None
    for acc in email_accounts:
        if acc.get('enabled', True):
            account = acc
            break
    
    if not account:
        print("❌ 没有启用的邮件账户")
        return
    
    print(f"📧 测试账户: {account.get('email', 'unknown')}")
    
    # 创建邮件客户端
    client = SimpleEmailClient(account)
    
    # 测试不同数量的邮件获取
    test_limits = [50, 100, 150, 200]
    
    for limit in test_limits:
        print(f"\n📊 测试获取 {limit} 封邮件:")
        
        start_time = time.time()
        emails = client.fetch_emails(limit=limit)
        duration = time.time() - start_time
        
        if emails:
            speed = len(emails) / duration if duration > 0 else 0
            print(f"   ✅ 成功获取 {len(emails)} 封邮件")
            print(f"   ⏱️  耗时: {duration:.2f}秒")
            print(f"   🚀 速度: {speed:.1f}封/秒")
            print(f"   📈 平均每封: {duration/len(emails)*1000:.0f}毫秒")
        else:
            print(f"   ❌ 获取失败，耗时: {duration:.2f}秒")
        
        # 断开连接，确保下次测试重新连接
        client.disconnect()
        time.sleep(1)  # 短暂休息
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")


def main():
    """主函数"""
    try:
        test_email_fetch_speed()
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
