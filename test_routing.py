#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件路由功能测试脚本 - Linus式验证
测试所有路由组件的正确性和性能
"""

import os
import sys
import time
from datetime import datetime

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from routing.workparam_loader import WorkParamLoader
from routing.nbi_extractor import NBIExtractor
from routing.workparam_router import WorkParamRouter
from config.config_manager import config_manager
from utils.logger import setup_logger, get_logger

# 初始化日志
setup_logger()
logger = get_logger(__name__)


def test_workparam_loader():
    """测试工参加载器"""
    print("\n" + "="*50)
    print("🧪 测试工参加载器")
    print("="*50)
    
    loader = WorkParamLoader(".")
    
    # 测试加载工参文件
    success = loader.load_workparams(
        "菏泽联通4G现网工参-2025-08-18.xlsx",
        "菏泽联通5G-现网工参-2025-08-18.xlsx"
    )
    
    if success:
        stats = loader.get_stats()
        print(f"✅ 工参加载成功")
        print(f"   总记录数: {stats['total_records']}")
        print(f"   加载时间: {stats['last_load_time']}")
        
        # 测试查找功能
        test_nbi_ids = ["********", "*********", "*********"]  # 测试存在和不存在的ID
        
        for nbi_id in test_nbi_ids:
            county = loader.get_county_by_nbi_id(nbi_id)
            if county:
                print(f"   查找测试: {nbi_id} → {county}")
            else:
                print(f"   查找测试: {nbi_id} → 未找到")
        
        return True
    else:
        print("❌ 工参加载失败")
        return False


def test_nbi_extractor():
    """测试NBI ID提取器"""
    print("\n" + "="*50)
    print("🧪 测试NBI ID提取器")
    print("="*50)
    
    extractor = NBIExtractor()
    
    # 测试用例
    test_cases = [
        "告警内容：网元ID: ******** 发生故障",
        "基站ID：********* 信号异常",
        "NBI ID: 123456789 设备离线",
        "CELLID: 620882101 覆盖问题",
        "没有NBI ID的普通邮件内容",
        "网元编号：999888777 多个 基站ID：111222333 ID测试"
    ]
    
    success_count = 0
    for i, content in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {content[:30]}...")
        
        # 测试单个提取
        nbi_id = extractor.extract_nbi_id(content)
        if nbi_id:
            print(f"   提取结果: {nbi_id}")
            success_count += 1
        else:
            print(f"   提取结果: 未找到")
        
        # 测试批量提取
        all_ids = extractor.extract_all_nbi_ids(content)
        if all_ids:
            print(f"   批量提取: {all_ids}")
    
    print(f"\n✅ NBI提取测试完成，成功率: {success_count}/{len(test_cases)}")
    return success_count > 0


def test_workparam_router():
    """测试工参路由器"""
    print("\n" + "="*50)
    print("🧪 测试工参路由器")
    print("="*50)
    
    router = WorkParamRouter(".")
    
    # 配置路由器
    routing_config = {
        "enabled": True,
        "nbi_extraction": {
            "enabled": True,
            "patterns": []
        },
        "workparam_files": {
            "4g_file": "菏泽联通4G现网工参-2025-08-18.xlsx",
            "5g_file": "菏泽联通5G-现网工参-2025-08-18.xlsx"
        },
        "county_webhooks": {
            "牡丹区": "https://webhook.example.com/mudan",
            "定陶区": "https://webhook.example.com/dingtao",
            "default": "https://webhook.example.com/default"
        }
    }
    
    success = router.configure(routing_config)
    if not success:
        print("❌ 路由器配置失败")
        return False
    
    # 测试路由功能
    test_emails = [
        ("告警内容：网元ID: ******** 发生故障", "4G网元告警"),
        ("基站ID：********* 信号异常", "5G基站告警"),
        ("NBI ID: ********* 未知设备", "未知设备告警"),
        ("普通邮件内容，没有NBI ID", "普通邮件")
    ]
    
    success_count = 0
    for content, subject in test_emails:
        print(f"\n测试邮件: {subject}")
        print(f"   内容: {content[:50]}...")
        
        webhook, route_info = router.route_email(content, subject)
        
        print(f"   NBI ID: {route_info.get('nbi_id', '未提取')}")
        print(f"   区县: {route_info.get('county', '未找到')}")
        print(f"   Webhook: {webhook or '默认'}")
        print(f"   路由方式: {route_info.get('method', 'unknown')}")
        print(f"   成功: {route_info.get('success', False)}")
        
        if webhook:
            success_count += 1
    
    # 获取统计信息
    stats = router.get_stats()
    print(f"\n📊 路由统计:")
    print(f"   总路由次数: {stats['total_routed']}")
    print(f"   成功路由: {stats['successful_routes']}")
    print(f"   提取失败: {stats['failed_extractions']}")
    print(f"   路由失败: {stats['failed_routes']}")
    print(f"   默认路由: {stats['default_routes']}")
    
    print(f"\n✅ 路由测试完成，成功率: {success_count}/{len(test_emails)}")
    return success_count > 0


def test_performance():
    """测试性能"""
    print("\n" + "="*50)
    print("🧪 性能测试")
    print("="*50)
    
    router = WorkParamRouter(".")
    
    # 配置路由器
    routing_config = config_manager.get_routing_config()
    routing_config["enabled"] = True
    router.configure(routing_config)
    
    # 生成测试数据
    test_content = "告警内容：网元ID: ******** 发生严重故障，请立即处理"
    test_count = 1000
    
    print(f"测试路由 {test_count} 次...")
    
    start_time = time.time()
    
    for i in range(test_count):
        webhook, route_info = router.route_email(test_content, f"测试邮件{i}")
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / test_count * 1000  # 毫秒
    
    print(f"✅ 性能测试完成:")
    print(f"   总时间: {total_time:.3f}秒")
    print(f"   平均时间: {avg_time:.3f}毫秒/次")
    print(f"   吞吐量: {test_count/total_time:.1f}次/秒")
    
    return avg_time < 10  # 平均时间应小于10毫秒


def test_error_handling():
    """测试错误处理"""
    print("\n" + "="*50)
    print("🧪 错误处理测试")
    print("="*50)
    
    # 测试文件不存在的情况
    loader = WorkParamLoader(".")
    success = loader.load_workparams("不存在的文件.xlsx", "另一个不存在的文件.xlsx")
    print(f"文件不存在测试: {'✅ 正确处理' if not success else '❌ 应该失败'}")
    
    # 测试空内容提取
    extractor = NBIExtractor()
    nbi_id = extractor.extract_nbi_id("")
    print(f"空内容提取测试: {'✅ 正确处理' if not nbi_id else '❌ 应该返回None'}")
    
    # 测试无效正则模式
    invalid_extractor = NBIExtractor(["[invalid regex"])
    print(f"无效正则测试: {'✅ 正确处理' if len(invalid_extractor.compiled_patterns) == 0 else '❌ 应该编译失败'}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始邮件路由功能测试")
    print(f"测试时间: {datetime.now()}")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("工参加载器", test_workparam_loader()))
    test_results.append(("NBI提取器", test_nbi_extractor()))
    test_results.append(("工参路由器", test_workparam_router()))
    test_results.append(("性能测试", test_performance()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！路由功能可以正常使用。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
