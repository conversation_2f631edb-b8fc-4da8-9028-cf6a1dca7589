#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMAP连接池 - Linus式简洁设计
核心原则：复用连接，减少网络开销，保持简单
"""

import time
import threading
from typing import Dict, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class IMAPConnectionPool:
    """IMAP连接池 - 避免重复连接开销
    
    Linus式设计原则：
    1. 简单的连接复用：一个邮箱一个连接
    2. 自动清理：超时连接自动断开
    3. 线程安全：支持并发访问
    """
    
    def __init__(self, max_idle_time: int = 300):  # 5分钟超时
        self.connections: Dict[str, any] = {}  # email -> client
        self.last_used: Dict[str, float] = {}  # email -> timestamp
        self.max_idle_time = max_idle_time
        self._lock = threading.RLock()  # 线程安全锁
        
        logger.info(f"🔗 IMAP连接池初始化，空闲超时: {max_idle_time}秒")
    
    def get_connection(self, email_config: Dict) -> Optional[any]:
        """获取连接 - 复用或创建新连接"""
        email_addr = email_config.get('email', '')
        if not email_addr:
            return None
        
        with self._lock:
            # 检查是否有可用连接
            if email_addr in self.connections:
                client = self.connections[email_addr]
                
                # 检查连接是否还活着
                if self._is_connection_alive(client):
                    self.last_used[email_addr] = time.time()
                    logger.debug(f"🔗 复用连接: {email_addr}")
                    return client
                else:
                    # 连接已死，清理
                    logger.debug(f"🔗 连接已失效，清理: {email_addr}")
                    self._remove_connection(email_addr)
            
            # 创建新连接
            return self._create_new_connection(email_config)
    
    def _create_new_connection(self, email_config: Dict) -> Optional[any]:
        """创建新连接"""
        email_addr = email_config.get('email', '')
        
        try:
            from src.email_handler.simple_email_client import SimpleEmailClient
            client = SimpleEmailClient(email_config)
            
            if client.connect():
                self.connections[email_addr] = client
                self.last_used[email_addr] = time.time()
                logger.info(f"🔗 新建连接: {email_addr}")
                return client
            else:
                logger.error(f"🔗 连接失败: {email_addr}")
                return None
                
        except Exception as e:
            logger.error(f"🔗 创建连接异常: {email_addr} - {e}")
            return None
    
    def _is_connection_alive(self, client) -> bool:
        """检查连接是否还活着"""
        try:
            if not hasattr(client, 'is_connected') or not client.is_connected:
                return False
            
            # 简单的连接测试
            if hasattr(client, 'imap') and client.imap:
                # 发送NOOP命令测试连接
                status, _ = client.imap.noop()
                return status == 'OK'
            
            return False
            
        except Exception:
            return False
    
    def _remove_connection(self, email_addr: str):
        """移除连接"""
        try:
            if email_addr in self.connections:
                client = self.connections[email_addr]
                try:
                    client.disconnect()
                except:
                    pass
                
                del self.connections[email_addr]
                del self.last_used[email_addr]
                logger.debug(f"🔗 连接已移除: {email_addr}")
                
        except Exception as e:
            logger.error(f"🔗 移除连接失败: {email_addr} - {e}")
    
    def cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        idle_connections = []
        
        with self._lock:
            for email_addr, last_used in self.last_used.items():
                if current_time - last_used > self.max_idle_time:
                    idle_connections.append(email_addr)
            
            for email_addr in idle_connections:
                logger.info(f"🔗 清理空闲连接: {email_addr}")
                self._remove_connection(email_addr)
        
        if idle_connections:
            logger.info(f"🔗 清理了 {len(idle_connections)} 个空闲连接")
    
    def close_all_connections(self):
        """关闭所有连接"""
        with self._lock:
            email_addrs = list(self.connections.keys())
            for email_addr in email_addrs:
                self._remove_connection(email_addr)
            
            logger.info(f"🔗 已关闭所有连接，共 {len(email_addrs)} 个")
    
    def get_stats(self) -> Dict:
        """获取连接池统计信息"""
        with self._lock:
            return {
                'active_connections': len(self.connections),
                'connection_emails': list(self.connections.keys()),
                'max_idle_time': self.max_idle_time
            }


# 全局连接池实例
connection_pool = IMAPConnectionPool()
