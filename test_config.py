#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本 - 验证路由配置是否正确加载
"""

import os
import sys

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config_manager import config_manager
from routing.workparam_router import WorkParamRouter
from utils.logger import setup_logger, get_logger

# 初始化日志
setup_logger()
logger = get_logger(__name__)


def test_config_loading():
    """测试配置加载"""
    print("🧪 测试配置加载")
    print("="*50)
    
    # 获取路由配置
    routing_config = config_manager.get_routing_config()
    
    print(f"路由功能启用: {routing_config.get('enabled', False)}")
    print(f"NBI提取启用: {routing_config.get('nbi_extraction', {}).get('enabled', False)}")
    
    county_webhooks = routing_config.get('county_webhooks', {})
    print(f"\n配置的区县数量: {len(county_webhooks)}")
    
    for county, webhook in county_webhooks.items():
        if county == 'default':
            print(f"  默认Webhook: {webhook[:50]}...")
        else:
            print(f"  {county}: {webhook[:50]}...")
    
    return True


def test_router_initialization():
    """测试路由器初始化"""
    print("\n🧪 测试路由器初始化")
    print("="*50)
    
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    
    success = router.configure(routing_config)
    
    if success:
        print("✅ 路由器初始化成功")
        
        # 获取统计信息
        stats = router.get_stats()
        print(f"工参记录数: {stats['workparam_records']}")
        print(f"区县Webhook数: {stats['county_webhooks']}")
        print(f"默认Webhook: {'已配置' if stats['has_default_webhook'] else '未配置'}")
        
        return True
    else:
        print("❌ 路由器初始化失败")
        return False


def test_sample_routing():
    """测试示例路由"""
    print("\n🧪 测试示例路由")
    print("="*50)
    
    router = WorkParamRouter(".")
    routing_config = config_manager.get_routing_config()
    router.configure(routing_config)
    
    # 测试邮件样本
    test_emails = [
        ("告警通知：NBI ID: 620882 设备故障", "4G网元测试"),
        ("基站异常：NBI ID: 3183385 信号中断", "5G基站测试"),
        ("普通邮件内容，没有NBI ID", "普通邮件测试")
    ]
    
    for content, description in test_emails:
        print(f"\n📧 {description}")
        print(f"   内容: {content}")
        
        webhook, route_info = router.route_email(content, description)
        
        print(f"   NBI ID: {route_info.get('nbi_id', '未提取')}")
        print(f"   区县: {route_info.get('county', '未找到')}")
        print(f"   Webhook: {webhook[:50] + '...' if webhook and len(webhook) > 50 else webhook or '默认'}")
        print(f"   路由方式: {route_info.get('method', 'unknown')}")
        print(f"   成功: {'✅' if route_info.get('success') else '❌'}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始配置测试")
    print("="*60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("配置加载", test_config_loading()))
    test_results.append(("路由器初始化", test_router_initialization()))
    test_results.append(("示例路由", test_sample_routing()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 配置测试通过！路由功能已正确配置。")
        print("\n📋 下一步:")
        print("1. 将示例Webhook地址替换为真实的钉钉群地址")
        print("2. 启动主程序测试实际邮件路由")
        print("3. 监控路由日志，确保功能正常")
        return True
    else:
        print("⚠️ 配置测试失败，请检查配置文件。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
