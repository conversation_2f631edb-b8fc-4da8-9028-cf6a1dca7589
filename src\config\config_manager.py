# -*- coding: utf-8 -*-
"""
配置管理器
"""

import json
import os
from typing import Dict, List, Any, Optional
from cryptography.fernet import Fernet
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "config.json")
        self.key_file = os.path.join(config_dir, "key.key")
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 初始化加密密钥
        self._init_encryption_key()
        
        # 加载配置
        self.config = self._load_config()
    
    def _init_encryption_key(self):
        """初始化加密密钥"""
        if os.path.exists(self.key_file):
            with open(self.key_file, 'rb') as f:
                self.key = f.read()
        else:
            self.key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(self.key)
        
        self.cipher = Fernet(self.key)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "email_accounts": [],
            "webhooks": [],
            "keywords": ["告警通知"],
            "auto_refresh": {
                "enabled": True,
                "interval": 60
            },
            "ui_settings": {
                "window_size": [1200, 800],
                "window_position": [100, 100]
            },
            "performance": {
                "max_emails_per_fetch": 200,  # 每次获取的最大邮件数
                "concurrent_timeout": 30,     # 并发超时时间(秒)
                "connection_timeout": 15      # 连接超时时间(秒)
            },
            "content_processing": {
                "raw_content": True,          # True=原文输出, False=清理后输出
                "max_length": 4000           # 最大内容长度
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                return default_config
        else:
            return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info("配置文件保存成功")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def encrypt_password(self, password: str) -> str:
        """加密密码"""
        return self.cipher.encrypt(password.encode()).decode()
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        return self.cipher.decrypt(encrypted_password.encode()).decode()
    
    # 邮箱配置相关方法
    def get_email_accounts(self) -> List[Dict[str, Any]]:
        """获取邮箱账户列表"""
        return self.config.get("email_accounts", [])
    
    def add_email_account(self, account: Dict[str, Any]):
        """添加邮箱账户"""
        # 注意：密码应该在调用此方法前已经加密
        self.config["email_accounts"].append(account)
        self.save_config()
    
    def update_email_account(self, index: int, account: Dict[str, Any]):
        """更新邮箱账户"""
        if 0 <= index < len(self.config["email_accounts"]):
            # 注意：密码应该在调用此方法前已经加密
            self.config["email_accounts"][index] = account
            self.save_config()
    
    def remove_email_account(self, index: int):
        """删除邮箱账户"""
        if 0 <= index < len(self.config["email_accounts"]):
            del self.config["email_accounts"][index]
            self.save_config()
    
    # Webhook配置相关方法
    def get_webhooks(self) -> List[str]:
        """获取Webhook列表"""
        return self.config.get("webhooks", [])
    
    def add_webhook(self, webhook: str):
        """添加Webhook"""
        if webhook not in self.config["webhooks"]:
            self.config["webhooks"].append(webhook)
            self.save_config()
    
    def remove_webhook(self, webhook: str):
        """删除Webhook"""
        if webhook in self.config["webhooks"]:
            self.config["webhooks"].remove(webhook)
            self.save_config()
    
    # 关键字配置相关方法
    def get_keywords(self) -> List[str]:
        """获取关键字列表"""
        return self.config.get("keywords", ["告警通知"])
    
    def set_keywords(self, keywords: List[str]):
        """设置关键字列表"""
        self.config["keywords"] = keywords
        self.save_config()
    
    # 自动刷新配置相关方法
    def get_auto_refresh_config(self) -> Dict[str, Any]:
        """获取自动刷新配置"""
        return self.config.get("auto_refresh", {"enabled": True, "interval": 60})
    
    def set_auto_refresh_config(self, enabled: bool, interval: int):
        """设置自动刷新配置"""
        self.config["auto_refresh"] = {"enabled": enabled, "interval": interval}
        self.save_config()
    
    # UI设置相关方法
    def get_ui_settings(self) -> Dict[str, Any]:
        """获取UI设置"""
        return self.config.get("ui_settings", {"window_size": [1200, 800], "window_position": [100, 100]})
    
    def set_ui_settings(self, settings: Dict[str, Any]):
        """设置UI设置"""
        self.config["ui_settings"] = settings
        self.save_config()

    # 图片附件配置相关方法
    def get_image_attachment_config(self) -> Dict[str, Any]:
        """获取图片附件推送配置"""
        return self.config.get("image_attachment", {
            "enabled": False,  # 默认关闭
            "max_size_mb": 2,  # 最大2MB（图片类型限制）
            "max_count": 3,    # 最多推送3张图片
            "supported_formats": ["jpg", "jpeg", "png", "gif", "bmp"],
            "push_as_file": False,  # False=图片消息，True=文件消息
            "image_only": False  # True=仅推送图片，False=推送正文+图片
        })

    # 性能配置相关方法
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.config.get("performance", {
            "max_emails_per_fetch": 200,  # 每次获取的最大邮件数
            "concurrent_timeout": 30,     # 并发超时时间(秒)
            "connection_timeout": 15      # 连接超时时间(秒)
        })

    def set_performance_config(self, performance_config: Dict[str, Any]):
        """设置性能配置"""
        self.config["performance"] = performance_config
        self.save_config()

    def get_content_processing_config(self) -> Dict[str, Any]:
        """获取内容处理配置"""
        return self.config.get("content_processing", {
            "raw_content": True,    # 默认原文输出
            "max_length": 4000     # 默认最大长度
        })

    def set_content_processing_config(self, content_config: Dict[str, Any]):
        """设置内容处理配置"""
        self.config["content_processing"] = content_config
        self.save_config()

    def set_image_attachment_config(self, config: Dict[str, Any]):
        """设置图片附件推送配置"""
        self.config["image_attachment"] = config
        self.save_config()


# 全局配置管理器实例
config_manager = ConfigManager()
